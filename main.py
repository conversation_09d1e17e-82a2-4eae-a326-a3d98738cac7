from groq import Groq

# Initialize Groq client
client = Groq(api_key="********************************************************")

# Enhanced role & context + few-shot examples (sentiment-specific)
training_examples = """
You are a sentiment analysis agent with deep expertise in workplace psychology and human resources.

Role: Expert Sentiment Analyst AI
Domain: Analyze employee feedback related to categories like Diversity, Equity, Inclusion, Leadership, Policy, Workplace Culture, and Employee Engagement.
Task: Given a sentence related to workplace experience, analyze and classify its sentiment.
Output Format: Respond ONLY with one of the following words — Positive, Negative, or Neutral.
Thinking Process: Think carefully about the tone, intent, and emotional polarity of the sentence. Consider if the sentence expresses satisfaction, dissatisfaction, or neutrality. Do not assume based on topic, only sentiment. Avoid explanation — just the final sentiment output is needed.

Example 1:
Sentence: I absolutely love the work environment here.
Sentiment: Positive

Example 2:
Sentence: This place is terrible to work at.
Sentiment: Negative

Example 3:
Sentence: You have career development opportunities to learn and grow in your current role, Sometimes.
Sentiment: Neutral

Example 4:
Sentence: The management is helpful and encouraging.
Sentiment: Positive

Example 5:
Sentence: "You feel that your workplace or organisation’s policies support you no matter your background of experience and knowledge. Needs for improvement."
Sentiment: Neutral

Example 6:
Sentence: The workload is overwhelming and unfair.
Sentiment: Negative
"""

# 🔹 NEW: Category classification prompt template
category_instruction = """
You are an AI trained to identify one or more workplace categories that best match a given employee statement,sometimes the sentence can be indirectlt related to some categories so understand the context and find the best match.

Role: Expert Workplace Feedback Classifier 
Categories: Diversity, Equity, Inclusion, Leadership, Policy, Workplace Culture, Employee Engagement,strategic_alignment,culture_engagement,support_motivation,skill_development,

Task: Given a sentence or question, predict which category or categories it belongs to. Respond only with category names separated by commas. Do not explain.

Example 1:
Sentence: I love how inclusive our company is toward people of different backgrounds.
Category: Inclusion, Diversity

Example 2:
Sentence: My manager is very supportive and always motivates us.
Category: Leadership, Employee Engagement

Example 3:
Sentence: The new remote work policy is confusing and not employee-friendly.
Category: Policy, Workplace Culture

Example 4:
Sentence: Everyone gets equal chances at promotion here.
Category: Equity

Example 5:
Sentence: I don’t feel connected to the company’s mission lately.
Category: Employee Engagement

Sentence: {sentence}
Category:
"""

# ✅ Original function (untouched)
def predict_sentiment(user_sentence):
    full_prompt = f"""{training_examples}

Sentence: {user_sentence}
Sentiment:"""

    response = client.chat.completions.create(
        model="llama3-70b-8192",
        messages=[
            {"role": "user", "content": full_prompt}
        ],
        temperature=0.2,
    )

    return response.choices[0].message.content.strip()

# ✅ New function: Predict categories (can be multiple)
def predict_category(user_sentence):
    full_prompt = category_instruction.format(sentence=user_sentence)

    response = client.chat.completions.create(
        model="llama3-70b-8192",
        messages=[
            {"role": "user", "content": full_prompt}
        ],
        temperature=0.2,
    )

    return response.choices[0].message.content.strip()

# ✅ Test example
test_sentence = "Do you feel supported and respected at work?. Most of the time."

# Run both predictions
sentiment = predict_sentiment(test_sentence)
category = predict_category(test_sentence)

print("Predicted Sentiment:", sentiment)
print("Predicted Category:", category)
