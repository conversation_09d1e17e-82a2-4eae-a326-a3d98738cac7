<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SURVEY PAGE</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Bootstrap & Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary: #2f68c0;
            --primary-dark: #1c4a94;
            --gray: #94a3b8;
            --light: #f8fafc;
            --dark: #1e293b;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--light);
            color: var(--dark);
        }

        h1, h2, h3 {
            text-align: center;
            color: var(--primary-dark);
        }

        .container {
            background-color: white;
            padding: 60px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px auto;
            width: 95%;
            max-width: 1100px;
            border: 1px solid var(--gray);
        }

        .container h1 {
            font-size: 40px;
            color: var(--primary);
        }

        .container h2 {
            font-size: 30px;
        }

        .container h3 {
            font-size: 25px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
            max-width: 300px;
        }

        label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
        }

        select,
        input[type="text"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 1px solid var(--gray);
            border-radius: 4px;
        }

        .submit-btn {
            background-color: var(--primary);
            color: white;
            padding: 10px 20px;
            font-size: 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: block;
            margin-left: 74%;
        }

        .submit-btn:hover {
            background-color: var(--primary-dark);
        }

        .other-field {
            display: none;
        }

        hr {
            border: none;
            height: 2px;
            background-color: var(--gray);
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px;
            }

            .container h1 {
                font-size: 28px;
            }

            .container h2 {
                font-size: 24px;
            }

            .container h3 {
                font-size: 20px;
            }

            .submit-btn {
                font-size: 18px;
                padding: 8px 16px;
            }
        }

        @media (max-width: 480px) {
            .form-row {
                flex-direction: column;
            }

            .form-group {
                max-width: 100%;
            }

            .submit-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{ company }}</h1>
        <h2>Employee Information</h2>
        <h3>Let's Start With Your Basic Details</h3>
        <hr>
        <form method="POST">
            <div class="form-row">
                <div class="form-group">
                    <label for="gender">Gender</label>
                    <select id="gender" name="gender" required>
                        <option value="">Select</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="age_group">Age Group</label>
                    <select id="age_group" name="age_group" required>
                        <option value="">Select</option>
                        <option value="18-25">18-25</option>
                        <option value="26-35">26-35</option>
                        <option value="36-45">36-45</option>
                        <option value="46+">46+</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="tenure_group">Tenure Group</label>
                    <select id="tenure_group" name="tenure_group" required>
                        <option value="">Select</option>
                        <option value="0-1 year">30 days-1 year</option>
                        <option value="1-3 years">1-3 years</option>
                        <option value="3-5 years">3-5 years</option>
                        <option value="5+ years">5+ years</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" name="role" required>
                        <option value="">Select</option>
                        <option value="Junior Staff">Junior Staff</option>
                        <option value="Senior Staff">Senior Staff</option>
                        <option value="Manager">Manager</option>
                        <option value="Executive">Executive</option>
                        <option value="Trainee">Trainee</option>
                        <option value="Team">Team Member</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="department">Department</label>
                    <select id="department" name="department" required>
                        <option value="">Select</option>
                        <option value="HR">Human Resources & Admin</option>
                        <option value="Finance">Finance and Accounting</option>
                        <option value="Sales">Sales and Marketing</option>
                        <option value="Product">Product Development</option>
                        <option value="Technical">Technical</option>
                        <option value="Operations">Operations</option>
                        <option value="Procurements">Procurement</option>
                        <option value="Quality">Quality</option>
                        <option value="Business">Business Development</option>
                        <option value="Executive">Executive</option>
                        <option value="Leadership">Leadership</option>
                        <option value="Management">Management</option>
                        <option value="Others">Others (Please Specify)</option>
                    </select>
                </div>
                <div class="form-group">
                    <div id="otherDepartmentField" class="other-field">
                        <label for="other_department">Other</label>
                        <input type="text" id="other_department" name="other_department">
                    </div>
                </div>
            </div>

            <input type="submit" class="submit-btn" value="Next">
        </form>
    </div>

    <script>
        // Show "Other Department" input field if "Others" is selected
        document.getElementById('department').addEventListener('change', function () {
            const otherField = document.getElementById('otherDepartmentField');
            otherField.style.display = (this.value === 'Others') ? 'block' : 'none';
        });
    </script>
</body>
</html>
