<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Employee Test</title>
  <style>
    /* Base Reset */
    *, *::before, *::after {
      margin: 0; padding: 0; box-sizing: border-box;
    }

    html, body {
      font-family: 'Segoe UI', Tahoma, sans-serif;
      background: #f0f2f5;
      min-height: 100vh;
      color: #333;
    }

    body {
      padding: 20px;
      background-color: #dadada;
    }

    /* Main Layout */
    .container {
      max-width: 800px;
      margin: auto;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      overflow: hidden;
    }

    .header {
      background-color: #ffffff;
      color: white;
      text-align: center;
      padding: 30px 20px;
    }

    .header h2 {
      font-size: 2rem;
      font-weight: 600;
      color: #000000;
    }

    .form-container {
      padding: 30px 20px;
    }

    /* Progress Section */
    .progress-indicator {
      background: #fff;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 25px;
      text-align: center;
    }

    .progress-text {
      margin-bottom: 10px;
      font-weight: 600;
      color: #4a63e7;
    }

    .progress-bar {
      background: #e0e0e0;
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      background: #4a63e7;
      width: 0%;
      height: 100%;
      transition: width 0.3s ease;
    }

    /* Question Card */
    .question-card {
      background: #f7f7f7;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 25px;
      display: none;
    }

    .question-card.active {
      display: block;
      animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .question-card p {
      font-size: 1.1rem;
      margin-bottom: 15px;
      font-weight: 500;
    }

    .question-card strong {
      background: #4a63e7;
      color: white;
      padding: 5px 12px;
      border-radius: 20px;
      font-size: 0.9rem;
      display: inline-block;
      margin-bottom: 10px;
    }

    .question-card label {
      display: block;
      background: white;
      border: 2px solid #ddd;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .question-card label:hover {
      border-color: #18a025;
      background-color: #eef0ff;
    }

    .question-card input[type="radio"] {
      margin-right: 12px;
      accent-color: #4a63e7;
    }

    .question-card label:has(input:checked) {
      border-color: #4a63e7;
      background-color: #e0e7ff;
      color: #4a63e7;
    }

    /* Navigation / Buttons */
    .button-container {
      margin-top: 30px;
      text-align: center;
    }

    .button-container h2 {
      margin-bottom: 20px;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .button-wrapper {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
    }

    .nav-btn, .submit-btn {
      background: #4a63e7;
      color: white;
      border: none;
      padding: 14px 0;
      min-width: 140px;
      border-radius: 30px;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      transition: background 0.2s ease, transform 0.2s ease;
    }

    .nav-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .nav-btn:hover:not(:disabled),
    .submit-btn:hover {
      transform: translateY(-2px);
      background: #3a50c2;
    }

    .submit-btn {
      background: #28a745;
    }

    .submit-btn:hover {
      background: #218838;
    }

    /* Mobile */
    /* ───────────────── Tablet: up to 768 px ───────────────── */
@media (max-width: 768px) {
  .form-container      { padding: 24px; }
  .header h2           { font-size: 1.8rem; }

  /* Question card tweaks */
  .question-card       { padding: 18px; }
  .question-card p     { font-size: 1.05rem; }
  .question-card label { font-size: 0.98rem; padding: 12px 14px; }

  /* Buttons stay in one row and wrap if needed */
  .button-wrapper {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
  }

  /* Slightly narrower buttons for more fit */
  .nav-btn,
  .submit-btn {
    min-width: 120px;
    padding: 12px 0;
    font-size: 0.98rem;
  }
}

/* ───────────────── Mobile: up to 480 px ───────────────── */
@media (max-width: 480px) {
  .form-container      { padding: 20px; }
  .header h2           { font-size: 1.5rem; }

  .question-card       { padding: 16px; }
  .question-card p     { font-size: 0.95rem; }
  .question-card label { font-size: 0.9rem;  padding: 10px 12px; }

  .button-wrapper {
    gap: 10px;                 /* tighter gap on very small screens */
  }

  .nav-btn,
  .submit-btn {
    min-width: 110px;
    padding: 10px 0;
    font-size: 0.9rem;
  }
}

  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2>Survey</h2>
    </div>

    <div class="form-container">
      <!-- Progress Bar -->
      <div class="progress-indicator">
        <div class="progress-text">Question <span id="currentQuestion">1</span> of <span id="totalQuestions">0</span></div>
        <div class="progress-bar"><div class="progress-fill" id="progressFill"></div></div>
      </div>

      <form id="testForm" method="POST">
        <!-- Example question (Django loop will replace this) -->
        {% for q in questions %}
        <div class="question-card" data-question="{{ q.question_number }}">
          <p><strong>Q{{ q.question_number }}:</strong> {{ q.question_text }}</p>
          <input type="hidden" name="question_text_{{ q.question_number }}" value="{{ q.question_text }}" />
          <label><input type="radio" name="question_{{ q.question_number }}" value="{{ q.option_1 }}" required /> {{ q.option_1 }}</label>
          <label><input type="radio" name="question_{{ q.question_number }}" value="{{ q.option_2 }}" /> {{ q.option_2 }}</label>
          <label><input type="radio" name="question_{{ q.question_number }}" value="{{ q.option_3 }}" /> {{ q.option_3 }}</label>
          <label><input type="radio" name="question_{{ q.question_number }}" value="{{ q.option_4 }}" /> {{ q.option_4 }}</label>
        </div>
        {% endfor %}

        <!-- Navigation Buttons -->
        <div class="button-container">
          
          <div class="button-wrapper">
            <button type="button" class="nav-btn" id="backBtn" disabled>← Back</button>
            <button type="button" class="nav-btn" id="nextBtn" disabled>Next →</button>
            <button type="submit" class="submit-btn" id="submitBtn" style="display: none;">Submit</button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- JS Logic -->
  <script>
    let currentQuestionIndex = 0;
    let totalQuestions = 0;
    let userAnswers = {};

    document.addEventListener("DOMContentLoaded", function () {
      const cards = document.querySelectorAll(".question-card");
      const backBtn = document.getElementById("backBtn");
      const nextBtn = document.getElementById("nextBtn");
      const submitBtn = document.getElementById("submitBtn");
      const currentQ = document.getElementById("currentQuestion");
      const totalQ = document.getElementById("totalQuestions");
      const progressFill = document.getElementById("progressFill");

      totalQuestions = cards.length;
      totalQ.textContent = totalQuestions;

      if (totalQuestions > 0) {
        cards[0].classList.add("active");
        updateProgress();
      }

      function updateProgress() {
        currentQ.textContent = currentQuestionIndex + 1;
        let progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;
        progressFill.style.width = progress + "%";
      }

      function saveAnswer() {
        const activeCard = cards[currentQuestionIndex];
        const selected = activeCard.querySelector("input:checked");
        if (selected) {
          userAnswers[currentQuestionIndex] = selected.value;
        }
      }

      function updateButtons() {
        const selected = cards[currentQuestionIndex].querySelector("input:checked");
        backBtn.disabled = currentQuestionIndex === 0;
        nextBtn.disabled = !selected;

        if (currentQuestionIndex === totalQuestions - 1) {
          nextBtn.style.display = "none";
          submitBtn.style.display = "inline-block";
          submitBtn.disabled = !selected;
        } else {
          nextBtn.style.display = "inline-block";
          submitBtn.style.display = "none";
        }
      }

      cards.forEach(card => {
        const radios = card.querySelectorAll("input[type='radio']");
        radios.forEach(radio => {
          radio.addEventListener("change", updateButtons);
        });
      });

      nextBtn.addEventListener("click", () => {
        if (currentQuestionIndex < totalQuestions - 1) {
          saveAnswer();
          cards[currentQuestionIndex].classList.remove("active");
          currentQuestionIndex++;
          cards[currentQuestionIndex].classList.add("active");
          updateProgress();
          updateButtons();
        }
      });

      backBtn.addEventListener("click", () => {
        if (currentQuestionIndex > 0) {
          cards[currentQuestionIndex].classList.remove("active");
          currentQuestionIndex--;
          cards[currentQuestionIndex].classList.add("active");
          updateProgress();
          updateButtons();
        }
      });

      document.getElementById("testForm").addEventListener("submit", function (e) {
        saveAnswer();
        if (Object.keys(userAnswers).length !== totalQuestions) {
          e.preventDefault();
          alert("Please answer all questions before submitting.");
        } else {
          submitBtn.textContent = "Submitting...";
          submitBtn.disabled = true;
        }
      });

      updateButtons();
    });
  </script>
</body>
</html>
